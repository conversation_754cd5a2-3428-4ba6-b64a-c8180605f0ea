import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Table,
  Modal,
  Chip,
  ActionMenu,
  Select,
} from '@/shared/components/common';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import { ConfirmDeleteModal } from '@/modules/admin/marketplace/components/modals';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { NotificationUtil } from '@/shared/utils/notification';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ViewProductForm, AddProductForm, EditProductForm } from '@/modules/admin/marketplace/components/forms';

// Removed unused interface CreateProductPageResponse

// Interface cho API response từ backend (theo data thực tế)
interface CreateProductApiResponse {
  code: number;
  message: string;
  result: {
    product: Product & {
      soldCount?: number;
      canPurchase?: boolean;
    };
    uploadUrls: {
      productId: string;
      imagesUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }>;
      userManualUploadUrl: string;
      detailUploadUrl: string;
    };
  };
}

// Interface cho response trả về cho AddProductForm (khớp với AddProductForm.tsx)
interface CreateProductPageResponse {
  code: number;
  message: string;
  result: {
    product: {
      id: number;
      name: string;
      description: string;
      images: Array<{
        key: string;
        position: number;
        url: string;
      }>;
      listedPrice: number;
      discountedPrice: number;
      category: string;
      userManual?: string;
      detail?: string;
      sourceId: string;
      createdAt: number;
      updatedAt: number;
      seller: {
        id: number;
        name: string;
        avatar: string | null;
        email: string | null;
        phoneNumber: string | null;
        type: string;
      };
      soldCount: number;
      canPurchase: boolean;
      status: string;
    };
    uploadUrls: {
      productId: string;
      imagesUploadUrls?: Array<{
        url: string;
        key: string;
        index: number;
      }>;
      detailUploadUrl?: string;
      userManualUploadUrl?: string;
    };
  };
}

// Import types from EditProductForm
interface UpdateProductRequest {
  productInfo: {
    name: string;
    listedPrice: number;
    discountedPrice: number;
    description?: string;
  };
  images: Array<{
    operation: 'ADD' | 'DELETE';
    key?: string;
    mimeType?: string;
    size?: number;
    name?: string;
  }>;
  detailEdited: boolean;
  userManual: boolean;
  publishAfterUpdate?: boolean;
}

interface UpdateProductResponse {
  code: number;
  message: string;
  result: {
    product: Product;
    presignedUrlImage: Array<{
      index: number;
      uploadUrl: string;
    }>;
    presignedUrlDetail: string | null;
    presignedUrlUserManual: string | null;
    publishError?: string;
  };
}

// Interface cho API response từ backend cho update
interface UpdateProductApiResponse {
  code: number;
  message: string;
  result: Product & {
    presignedUrlImage?: Array<{
      index: number;
      uploadUrl: string;
    }>;
    presignedUrlDetail?: string;
    presignedUrlUserManual?: string;
    publishError?: string;
  };
}

// Import hooks từ module marketplace
import {
  useProducts,
  useProduct,
  useCreateProduct,
  useUpdateProduct,
  useDeleteProduct,
  useApproveProduct,
  useRejectProduct,
  useBatchDeleteProducts,
  useBatchUpdateProductStatus,
  usePublishProduct,
} from '@/modules/admin/marketplace/hooks/useProduct';

// Import types từ module marketplace
import {
  Product,
  ProductCategory,
  ProductStatus,
  ProductFilterParams,
  CreateProductDto,
} from '@/modules/admin/marketplace/types/product.types';

/**
 * Trang quản lý sản phẩm trong marketplace
 */
const ProductsPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const { success, error: showError } = useSmartNotification();
  const [products, setProducts] = useState<Product[]>([]);

  // Pagination state
  const [paginationMeta, setPaginationMeta] = useState({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 1,
    itemCount: 0
  });
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [productToView, setProductToView] = useState<Product | null>(null);
  const [productToEdit, setProductToEdit] = useState<Product | null>(null);
  const [productIdToEdit, setProductIdToEdit] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showApproveConfirm, setShowApproveConfirm] = useState(false);
  const [showRejectForm, setShowRejectForm] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // State cho batch operations
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [showBulkStatusUpdateModal, setShowBulkStatusUpdateModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<ProductStatus | ''>('');



  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // State cho filter
  const [selectedFilterValue, setSelectedFilterValue] = useState<string | number | boolean | undefined>('all');

  // Handler cho filter change
  const handleFilterChange = useCallback((_filterId: string, filterValue: string | number | boolean | undefined) => {
    setSelectedFilterValue(filterValue);
    setPaginationMeta(prev => ({
      ...prev,
      currentPage: 1 // Reset về trang 1 khi filter
    }));
  }, []);

  // Filter options cho ModernMenu
  const filterOptions = useMemo(
    () => [
      {
        id: 'all',
        label: t('admin:marketplace.product.allStatuses', 'Tất cả trạng thái'),
        icon: 'list',
        onClick: () => handleFilterChange('all', 'all')
      },
      {
        id: 'pending',
        label: t('admin:marketplace.product.status.PENDING', 'Chờ phê duyệt'),
        icon: 'clock',
        onClick: () => handleFilterChange('pending', ProductStatus.PENDING)
      },
      {
        id: 'approved',
        label: t('admin:marketplace.product.status.APPROVED', 'Đã phê duyệt'),
        icon: 'check',
        onClick: () => handleFilterChange('approved', ProductStatus.APPROVED)
      },
      {
        id: 'rejected',
        label: t('admin:marketplace.product.status.REJECTED', 'Đã từ chối'),
        icon: 'x',
        onClick: () => handleFilterChange('rejected', ProductStatus.REJECTED)
      },
      {
        id: 'draft',
        label: t('admin:marketplace.product.status.DRAFT', 'Bản nháp'),
        icon: 'file',
        onClick: () => handleFilterChange('draft', ProductStatus.DRAFT)
      },
      {
        id: 'deleted',
        label: t('admin:marketplace.product.status.DELETED', 'Đã xóa'),
        icon: 'trash',
        onClick: () => handleFilterChange('deleted', ProductStatus.DELETED)
      },
    ],
    [t, handleFilterChange]
  );

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<ProductFilterParams>(() => {
    const params: ProductFilterParams = {
      page: paginationMeta.currentPage,
      limit: paginationMeta.itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    // Thêm filter status nếu không phải 'all'
    if (selectedFilterValue !== 'all') {
      params.status = selectedFilterValue as ProductStatus;
    }

    return params;
  }, [paginationMeta.currentPage, paginationMeta.itemsPerPage, searchTerm, sortBy, sortDirection, selectedFilterValue]);

  // Hooks để gọi API
  const {
    data: productsData,
    isLoading: isLoadingProducts,
    error: productsError,
    refetch: refetchProducts,
  } = useProducts(queryParams);

  const { mutateAsync: createProduct } = useCreateProduct();
  const { mutateAsync: updateProduct } = useUpdateProduct();
  const { mutateAsync: deleteProduct } = useDeleteProduct();
  const { mutateAsync: approveProduct } = useApproveProduct();
  const { mutateAsync: rejectProduct } = useRejectProduct();
  const { mutateAsync: batchDeleteProducts } = useBatchDeleteProducts();
  const { mutateAsync: batchUpdateProductStatus } = useBatchUpdateProductStatus();
  const { mutateAsync: publishProduct } = usePublishProduct();

  // Hook để lấy chi tiết product khi edit
  const {
    data: productDetailData,
    isLoading: isLoadingProductDetail,
    error: productDetailError,
    refetch: refetchProductDetail,
  } = useProduct(productIdToEdit || '');

  // Debug logging cho product detail hook
  useEffect(() => {
    console.log('🔍 [ProductsPage] Product detail hook state:', {
      productIdToEdit,
      isLoadingProductDetail,
      hasProductDetailData: !!productDetailData,
      productDetailError: productDetailError?.message || null
    });
  }, [productIdToEdit, isLoadingProductDetail, productDetailData, productDetailError]);

  // Xử lý submit form tạo sản phẩm
  const handleSubmitCreateProduct = useCallback(
    async (productData: CreateProductDto): Promise<CreateProductPageResponse> => {
      try {
        // Gọi API tạo sản phẩm và trả về response
        const response = await createProduct(productData) as unknown as CreateProductApiResponse;
        console.log('🔍 Create product response:', response);

        // Transform response theo structure thực tế từ API
        // Response structure: { code, message, result: { product: {...}, uploadUrls: {...} } }
        console.log('🔍 Raw API response:', JSON.stringify(response, null, 2));

        // Transform để khớp với CreateProductResponse interface trong AddProductForm
        const createProductResponse = {
          code: response.code,
          message: response.message,
          result: {
            product: {
              ...response.result.product,
              id: Number(response.result.product.id), // Convert string to number
              images: Array.isArray(response.result.product.images)
                ? response.result.product.images.map((img: unknown, index: number) => {
                    if (typeof img === 'string') {
                      return { key: `image-${index}`, position: index, url: img };
                    }
                    const imageObj = img as { key?: string; position?: number; url?: string };
                    return {
                      key: imageObj.key || `image-${index}`,
                      position: imageObj.position ?? index,
                      url: imageObj.url || ''
                    };
                  })
                : [],
              createdAt: Number(response.result.product.createdAt), // Convert to number
              updatedAt: Number(response.result.product.updatedAt), // Convert to number
              seller: {
                ...response.result.product.seller,
                id: response.result.product.seller?.id || 0,
                email: response.result.product.seller?.email || null,
                phoneNumber: response.result.product.seller?.phoneNumber || null,
                avatar: response.result.product.seller?.avatar || null,
              },
              soldCount: response.result.product.soldCount || 0,
              canPurchase: response.result.product.canPurchase !== false,
            },
            uploadUrls: response.result.uploadUrls
          }
        };

        return createProductResponse;
      } catch (err) {
        console.error('Error creating product:', err);
        showError({
          title: t('admin:marketplace.product.createError', 'Lỗi'),
          message: t('admin:marketplace.product.createErrorMessage', 'Không thể tạo sản phẩm. Vui lòng thử lại.'),
        });
        throw err; // Re-throw để AddProductForm có thể xử lý error
      }
    },
    [createProduct, showError, t]
  );

  // Xử lý submit form chỉnh sửa sản phẩm
  const handleSubmitEditProduct = useCallback(
    async (request: UpdateProductRequest): Promise<UpdateProductResponse> => {
      if (!productToEdit) {
        throw new Error('No product selected for editing');
      }

      try {
        console.log('🔍 Updating product ID:', productToEdit.id);
        console.log('🔍 Update request:', JSON.stringify(request, null, 2));

        // Transform request to match API DTO
        const apiRequest = {
          ...request,
          productInfo: {
            ...request.productInfo,
            description: request.productInfo.description || ''
          }
        };

        // Gọi API cập nhật với request format mới
        const updateResponse = await updateProduct({
          id: productToEdit.id,
          data: apiRequest,
        }) as UpdateProductApiResponse;

        console.log('✅ Product updated successfully:', updateResponse);

        // Transform response to match EditProductForm expectation
        // Backend trả về structure như bạn cung cấp: result chính là product object với thêm presigned URLs
        const { presignedUrlImage, presignedUrlDetail, presignedUrlUserManual, publishError, ...productInfo } = updateResponse.result;

        const transformedResponse: UpdateProductResponse = {
          code: updateResponse.code,
          message: updateResponse.message,
          result: {
            product: productInfo as Product,
            presignedUrlImage: presignedUrlImage || [],
            presignedUrlDetail: presignedUrlDetail || null,
            presignedUrlUserManual: presignedUrlUserManual || null,
            publishError: publishError,
          }
        };

        // Invalidate và refetch data để cập nhật cache
        await refetchProducts();

        // Nếu đang edit một product cụ thể, cũng refetch detail của product đó
        if (productIdToEdit) {
          await refetchProductDetail();
        }

        setProductToEdit(null);
        setProductIdToEdit(null);
        hideEditForm();
        success({
          title: t('admin:marketplace.product.updateSuccess', 'Thành công'),
          message: t('admin:marketplace.product.updateSuccessMessage', 'Sản phẩm đã được cập nhật thành công'),
        });

        return transformedResponse;
      } catch (err) {
        console.error('Error updating product:', err);
        showError({
          title: t('admin:marketplace.product.updateError', 'Lỗi'),
          message: t('admin:marketplace.product.updateErrorMessage', 'Không thể cập nhật sản phẩm. Vui lòng thử lại.'),
        });
        throw err;
      }
    },
    [productToEdit, productIdToEdit, updateProduct, hideEditForm, success, showError, t, refetchProducts, refetchProductDetail]
  );

  // Xử lý thay đổi selection
  const handleSelectionChange = useCallback((selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  }, []);

  // Xử lý xóa nhiều sản phẩm
  const handleBulkDelete = useCallback(async () => {
    try {
      const productIds = selectedRowKeys.map(key => Number(key));
      await batchDeleteProducts(productIds);
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);
      success({
        title: t('admin:marketplace.product.bulkDeleteSuccess', 'Thành công'),
        message: t('admin:marketplace.product.bulkDeleteSuccessMessage', 'Đã xóa {{count}} sản phẩm thành công', { count: productIds.length }),
      });
    } catch (err) {
      console.error('Error deleting products:', err);
      showError({
        title: t('admin:marketplace.product.bulkDeleteError', 'Lỗi'),
        message: t('admin:marketplace.product.bulkDeleteErrorMessage', 'Không thể xóa sản phẩm. Vui lòng thử lại.'),
      });
    }
  }, [selectedRowKeys, batchDeleteProducts, success, showError, t]);

  // Xử lý cập nhật trạng thái nhiều sản phẩm
  const handleBulkStatusUpdate = useCallback(async () => {
    if (!selectedStatus) return;

    try {
      const productIds = selectedRowKeys.map(key => Number(key));
      await batchUpdateProductStatus({ productIds, status: selectedStatus });
      setSelectedRowKeys([]);
      setSelectedStatus('');
      setShowBulkStatusUpdateModal(false);
      success({
        title: t('admin:marketplace.product.bulkUpdateSuccess', 'Thành công'),
        message: t('admin:marketplace.product.bulkUpdateSuccessMessage', 'Đã cập nhật trạng thái cho {{count}} sản phẩm', { count: productIds.length }),
      });
    } catch (err) {
      console.error('Error updating product status:', err);
      showError({
        title: t('admin:marketplace.product.bulkUpdateError', 'Lỗi'),
        message: t('admin:marketplace.product.bulkUpdateErrorMessage', 'Không thể cập nhật trạng thái sản phẩm. Vui lòng thử lại.'),
      });
    }
  }, [selectedRowKeys, selectedStatus, batchUpdateProductStatus, success, showError, t]);

  // Xử lý đăng bán sản phẩm (publish)
  const handlePublishProduct = useCallback(async (product: Product) => {
    try {
      await publishProduct(product.id);
      success({
        title: t('admin:marketplace.product.publishSuccess', 'Thành công'),
        message: t('admin:marketplace.product.publishSuccessMessage', 'Sản phẩm "{{name}}" đã được đăng bán thành công', { name: product.name }),
      });
    } catch (err) {
      console.error('Error publishing product:', err);
      showError({
        title: t('admin:marketplace.product.publishError', 'Lỗi'),
        message: t('admin:marketplace.product.publishErrorMessage', 'Không thể đăng bán sản phẩm. Vui lòng thử lại.'),
      });
    }
  }, [publishProduct, success, showError, t]);

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (productsData) {
      setProducts(productsData.items);

      // Update pagination meta data
      setPaginationMeta({
        currentPage: productsData.meta.currentPage,
        itemsPerPage: productsData.meta.itemsPerPage,
        totalItems: productsData.meta.totalItems,
        totalPages: productsData.meta.totalPages,
        itemCount: productsData.meta.itemCount
      });
    }
  }, [productsData, productsError]);

  // Cập nhật productToEdit khi có productDetailData
  useEffect(() => {
    if (productDetailData) {
      console.log('🔍 [ProductsPage] Product detail loaded:', productDetailData);
      setProductToEdit(productDetailData);
    }
  }, [productDetailData]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setPaginationMeta(prev => ({
        ...prev,
        currentPage: page
      }));

      if (newPageSize !== paginationMeta.itemsPerPage) {
        setPaginationMeta(prev => ({
          ...prev,
          itemsPerPage: newPageSize,
          currentPage: 1 // Reset về trang 1 khi thay đổi số mục trên trang
        }));
      }
    },
    [paginationMeta.itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setPaginationMeta(prev => ({
      ...prev,
      currentPage: 1 // Reset về trang 1 khi tìm kiếm
    }));
  }, []);



  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: handleSearch,
      setSelectedFilterId: () => {}, // Không sử dụng selectedFilterId
      setDateRange: () => {}, // Không sử dụng date range
      handleSortChange: handleSortChange,
      selectedFilterValue: selectedFilterValue,
      filterValueLabelMap: {
        [ProductStatus.PENDING]: t('admin:marketplace.product.status.PENDING', 'Chờ phê duyệt'),
        [ProductStatus.APPROVED]: t('admin:marketplace.product.status.APPROVED', 'Đã phê duyệt'),
        [ProductStatus.REJECTED]: t('admin:marketplace.product.status.REJECTED', 'Đã từ chối'),
        [ProductStatus.DRAFT]: t('admin:marketplace.product.status.DRAFT', 'Bản nháp'),
        [ProductStatus.DELETED]: t('admin:marketplace.product.status.DELETED', 'Đã xóa'),
      },
      t,
    });



  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProductToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!productToDelete) return;

    try {
      await deleteProduct(productToDelete.id);
      setShowDeleteConfirm(false);
      setProductToDelete(null);
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  }, [productToDelete, deleteProduct]);

  // Xử lý hiển thị popup xác nhận phê duyệt
  const handleShowApproveConfirm = useCallback((product: Product) => {
    setProductToView(product);
    setShowApproveConfirm(true);
  }, []);

  // Xử lý hủy phê duyệt
  const handleCancelApprove = useCallback(() => {
    setShowApproveConfirm(false);
    setProductToView(null);
  }, []);

  // Xử lý xác nhận phê duyệt
  const handleConfirmApprove = useCallback(async () => {
    if (!productToView) return;

    try {
      await approveProduct(productToView.id);
      setShowApproveConfirm(false);
      setProductToView(null);
    } catch (error) {
      console.error('Error approving product:', error);
    }
  }, [productToView, approveProduct]);

  // Xử lý hiển thị form từ chối
  const handleShowRejectForm = useCallback((product: Product) => {
    setProductToView(product);
    setRejectReason('');
    setShowRejectForm(true);
  }, []);

  // Xử lý hủy từ chối
  const handleCancelReject = useCallback(() => {
    setShowRejectForm(false);
    setProductToView(null);
    setRejectReason('');
  }, []);

  // Xử lý xác nhận từ chối
  const handleConfirmReject = useCallback(async () => {
    if (!productToView || !rejectReason) return;

    try {
      await rejectProduct({ id: productToView.id, reason: rejectReason });
      setShowRejectForm(false);
      setProductToView(null);
      setRejectReason('');
    } catch (error) {
      console.error('Error rejecting product:', error);
    }
  }, [productToView, rejectReason, rejectProduct]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết sản phẩm
  const handleShowViewForm = useCallback(
    (product: Product) => {
      setProductToView(product);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  // Xử lý hiển thị form chỉnh sửa sản phẩm
  const handleShowEditForm = useCallback(
    (product: Product) => {
      console.log('🔍 [ProductsPage] ==================== OPENING EDIT FORM ====================');
      console.log('🔍 [ProductsPage] Product from table:', {
        id: product.id,
        name: product.name,
        description: product.description,
        listedPrice: product.listedPrice,
        discountedPrice: product.discountedPrice,
        category: product.category,
        detail: product.detail,
        userManual: product.userManual,
        images: product.images
      });
      console.log('🔍 [ProductsPage] Setting productIdToEdit to:', product.id.toString());

      // Chỉ set productIdToEdit để trigger useProduct hook
      // productToEdit sẽ được set trong useEffect khi productDetailData có dữ liệu
      setProductIdToEdit(product.id.toString());
      setProductToEdit(null); // Reset để đảm bảo form không hiển thị dữ liệu cũ
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý hủy form chỉnh sửa
  const handleCancelEditForm = useCallback(() => {
    setProductToEdit(null);
    setProductIdToEdit(null);
    hideEditForm();
  }, [hideEditForm]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('admin:marketplace.product.table.name', 'Tên sản phẩm'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'category',
        title: t('admin:marketplace.product.table.category', 'Loại'),
        dataIndex: 'category',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const category = value as ProductCategory;
          return (
            <Chip size="sm" variant="default">
              {t(`admin:marketplace.product.category.${category}`, category)}
            </Chip>
          );
        },
      },
      {
        key: 'discountedPrice',
        title: t('admin:marketplace.product.table.price', 'Giá'),
        dataIndex: 'discountedPrice',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <span>{String(value)} points</span>;
        },
      },
      {
        key: 'status',
        title: t('admin:marketplace.product.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as ProductStatus;
          let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

          switch (status) {
            case ProductStatus.APPROVED:
              variant = 'success';
              break;
            case ProductStatus.PENDING:
              variant = 'warning';
              break;
            case ProductStatus.REJECTED:
              variant = 'danger';
              break;
            case ProductStatus.DRAFT:
              variant = 'default';
              break;
            case ProductStatus.DELETED:
              variant = 'danger';
              break;
          }

          return (
            <Chip size="sm" variant={variant}>
              {t(`admin:marketplace.product.status.${status}`, status)}
            </Chip>
          );
        },
      },
      {
        key: 'seller',
        title: t('admin:marketplace.product.table.seller', 'Người bán'),
        dataIndex: 'seller',
        width: '15%',
        render: (value: unknown) => {
          const seller = value as { name: string; email?: string };
          return (
            <div className="flex flex-col">
              <span>{seller.name}</span>
              {seller.email && <span className="text-xs text-gray-500">{seller.email}</span>}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('admin:marketplace.product.table.actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: Product) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleShowEditForm(record),
            },
            // Publish button - chỉ hiển thị khi status là DRAFT
            ...(record.status === ProductStatus.DRAFT ? [{
              id: 'publish',
              label: t('admin:marketplace.product.publish', 'Đăng bán'),
              icon: 'send',
              onClick: () => handlePublishProduct(record),
            }] : []),
            ...(record.status === ProductStatus.PENDING
              ? [
                  {
                    id: 'approve',
                    label: t('admin:marketplace.product.approve', 'Phê duyệt'),
                    icon: 'check',
                    onClick: () => handleShowApproveConfirm(record),
                  },
                  {
                    id: 'reject',
                    label: t('admin:marketplace.product.reject', 'Từ chối'),
                    icon: 'close',
                    onClick: () => handleShowRejectForm(record),
                  },
                ]
              : []),
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('admin:marketplace.product.moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [
    t,
    visibleColumns,
    handleShowViewForm,
    handleShowEditForm,
    handleShowApproveConfirm,
    handleShowRejectForm,
    handlePublishProduct,
  ]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            items={filterOptions}
            additionalIcons={[
              {
                icon: 'edit',
                tooltip: t('admin:marketplace.product.bulkUpdateStatus', 'Cập nhật trạng thái'),
                variant: 'primary',
                onClick: () => {
                  if (selectedRowKeys.length > 0 && (productsData?.items?.length ?? 0) > 0) {
                    setShowBulkStatusUpdateModal(true);
                  } else {
                    NotificationUtil.info({
                      message: t(
                        'admin:marketplace.product.selectProductsToUpdate',
                        'Vui lòng chọn ít nhất một sản phẩm để cập nhật trạng thái'
                      ),
                      duration: 3000,
                    });
                  }
                },
                className: 'text-blue-500',
                condition: selectedRowKeys.length > 0 && (productsData?.items?.length ?? 0) > 0,
              },
              {
                icon: 'trash',
                tooltip: t('common.bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: () => {
                  if (selectedRowKeys.length > 0 && (productsData?.items?.length ?? 0) > 0) {
                    setShowBulkDeleteConfirm(true);
                  } else {
                    NotificationUtil.info({
                      message: t(
                        'admin:marketplace.product.selectProductsToDelete',
                        'Vui lòng chọn ít nhất một sản phẩm để xóa'
                      ),
                      duration: 3000,
                    });
                  }
                },
                className: 'text-red-500',
                condition: selectedRowKeys.length > 0 && (productsData?.items?.length ?? 0) > 0,
              },
            ]}
          />

        {/* Hiển thị ActiveFilters */}
        <ActiveFilters
          searchTerm={searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={selectedFilterValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          sortBy={sortBy}
          sortDirection={sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />
        </div>



        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <AddProductForm
            onSubmit={handleSubmitCreateProduct}
            onCancel={hideCreateForm}
            onSuccess={() => {
              hideCreateForm();
              success({
                title: t('admin:marketplace.product.createSuccess', 'Thành công'),
                message: t('admin:marketplace.product.createSuccessMessage', 'Sản phẩm đã được tạo thành công'),
              });
            }}
          />
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          <ViewProductForm
            product={productToView}
            onClose={hideViewForm}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {productToEdit && !isLoadingProductDetail && (
            <EditProductForm
              product={productToEdit}
              onSubmit={handleSubmitEditProduct}
              onCancel={handleCancelEditForm}
            />
          )}
          {isLoadingProductDetail && (
            <div className="p-6 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <Typography variant="body2" className="text-muted">
                  {t('common:loading', 'Đang tải dữ liệu sản phẩm...')}
                </Typography>
              </div>
            </div>
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<Product>
            columns={filteredColumns}
            data={products}
            rowKey="id"
            loading={isLoadingProducts}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            rowSelection={{
              selectedRowKeys,
              onChange: handleSelectionChange,
            }}
            pagination={{
              current: paginationMeta.currentPage,
              pageSize: paginationMeta.itemsPerPage,
              total: paginationMeta.totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t(
          'admin:marketplace.product.confirmDeleteMessage',
          'Bạn có chắc chắn muốn xóa sản phẩm này?'
        )}
        itemName={productToDelete?.name}
      />

      {/* Modal xác nhận phê duyệt */}
      <ConfirmDeleteModal
        isOpen={showApproveConfirm}
        onClose={handleCancelApprove}
        onConfirm={handleConfirmApprove}
        title={t('admin.marketplace.product.approveTitle', 'Phê duyệt sản phẩm')}
        message={t(
          'admin.marketplace.product.confirmApproveMessage',
          'Bạn có chắc chắn muốn phê duyệt sản phẩm này?'
        )}
        itemName={productToView?.name}
      />

      {/* Modal từ chối sản phẩm */}
      <Modal
        isOpen={showRejectForm}
        onClose={handleCancelReject}
        title={t('admin.marketplace.product.rejectTitle', 'Từ chối sản phẩm')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelReject}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmReject} disabled={!rejectReason}>
              {t('admin.marketplace.product.reject', 'Từ chối')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-2">
            {t(
              'admin.marketplace.product.confirmRejectMessage',
              'Vui lòng nhập lý do từ chối sản phẩm:'
            )}
          </Typography>
          {productToView && (
            <Typography variant="body2" className="mb-4 font-semibold">
              {productToView.name}
            </Typography>
          )}
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={4}
            value={rejectReason}
            onChange={e => setRejectReason(e.target.value)}
            placeholder={t(
              'admin.marketplace.product.rejectReasonPlaceholder',
              'Nhập lý do từ chối...'
            )}
          />
        </div>
      </Modal>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={() => setShowBulkDeleteConfirm(false)}
        onConfirm={handleBulkDelete}
        title={t('common:confirmBulkDelete', 'Xác nhận xóa nhiều')}
        message={t(
          'admin:marketplace.product.confirmBulkDeleteMessage',
          'Bạn có chắc chắn muốn xóa {{count}} sản phẩm đã chọn?',
          { count: selectedRowKeys.length }
        )}
      />

      {/* Modal cập nhật trạng thái nhiều */}
      <Modal
        isOpen={showBulkStatusUpdateModal}
        onClose={() => setShowBulkStatusUpdateModal(false)}
        title={t('admin:marketplace.product.bulkUpdateStatus', 'Cập nhật trạng thái nhiều sản phẩm')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowBulkStatusUpdateModal(false)}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="primary"
              onClick={handleBulkStatusUpdate}
              disabled={!selectedStatus}
            >
              {t('common:update', 'Cập nhật')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-4">
            {t(
              'admin:marketplace.product.confirmBulkStatusUpdateMessage',
              'Chọn trạng thái mới cho {{count}} sản phẩm đã chọn:',
              { count: selectedRowKeys.length }
            )}
          </Typography>
          <Select
            placeholder={t('admin:marketplace.product.selectStatus', 'Chọn trạng thái')}
            value={selectedStatus}
            onChange={(value) => setSelectedStatus(value as ProductStatus | '')}
            options={[
              {
                value: ProductStatus.APPROVED,
                label: t('admin:marketplace.product.status.APPROVED', 'Đã phê duyệt'),
              },
              {
                value: ProductStatus.PENDING,
                label: t('admin:marketplace.product.status.PENDING', 'Chờ phê duyệt'),
              },
              {
                value: ProductStatus.REJECTED,
                label: t('admin:marketplace.product.status.REJECTED', 'Đã từ chối'),
              },
            ]}
            fullWidth
          />
        </div>
      </Modal>
    </div>
  );
};

export default ProductsPage;
