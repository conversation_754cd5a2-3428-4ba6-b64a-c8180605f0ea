import { useQuery } from '@tanstack/react-query';
import { MarketplaceApiService } from '@/modules/marketplace/services/marketplace-api.service';
import { PRODUCT_QUERY_KEYS } from '@/modules/marketplace/constants/product-query-keys';

/**
 * Hook đơn giản để lấy số lượng sản phẩm trong giỏ hàng
 * Được tách riêng để tránh circular dependency
 */
export const useCartCount = () => {
  const cartQuery = useQuery({
    queryKey: PRODUCT_QUERY_KEYS.CART,
    queryFn: () => MarketplaceApiService.getCart(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Chỉ retry 1 lần để tránh spam
    refetchOnWindowFocus: false, // Không refetch khi focus window
    // Chỉ fetch khi user đã login
    enabled: true,
  });

  // T<PERSON>h số lượng sản phẩm trong giỏ hàng (mỗi item = 1 sản phẩm)
  const totalItems = cartQuery.data?.items?.length || 0;

  // Debug logging
  console.log('🛒 [useCartCount] Debug:', {
    hasData: !!cartQuery.data,
    itemsLength: cartQuery.data?.items?.length,
    totalItems,
    isLoading: cartQuery.isLoading,
    error: cartQuery.error?.message,
    cartData: cartQuery.data
  });

  return {
    totalItems,
    isLoading: cartQuery.isLoading,
    error: cartQuery.error,
  };
};
