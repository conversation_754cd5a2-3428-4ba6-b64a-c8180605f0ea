import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProductForSale, ProductForSaleFilterParams } from '../types/product-for-sale.types';
import { ProductForSaleService } from '../services/product-for-sale.service';

// Key cho React Query
const PRODUCTS_FOR_SALE_QUERY_KEY = 'products-for-sale';

/**
 * Hook để lấy danh sách sản phẩm đăng bán
 */
export const useProductsForSale = (params?: ProductForSaleFilterParams) => {
  return useQuery({
    queryKey: [PRODUCTS_FOR_SALE_QUERY_KEY, params],
    queryFn: () => ProductForSaleService.getProductsForSale(params),
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy chi tiết sản phẩm đăng bán
 */
export const useProductForSale = (id: string) => {
  return useQuery({
    queryKey: [PRODUCTS_FOR_SALE_QUERY_KEY, id],
    queryFn: () => ProductForSaleService.getProductForSale(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo sản phẩm đăng bán mới
 */
export const useCreateProductForSale = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<ProductForSale, 'id' | 'createdAt' | 'updatedAt'>) =>
      ProductForSaleService.createProductForSale(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PRODUCTS_FOR_SALE_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật sản phẩm đăng bán
 */
export const useUpdateProductForSale = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ProductForSale> }) =>
      ProductForSaleService.updateProductForSale(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [PRODUCTS_FOR_SALE_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [PRODUCTS_FOR_SALE_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa sản phẩm đăng bán
 */
export const useDeleteProductForSale = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => ProductForSaleService.deleteProductForSale(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PRODUCTS_FOR_SALE_QUERY_KEY] });
    },
  });
};
